"use client";

import { useEffect, useRef, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	Send,
	ArrowLeft,
	Shield,
	CheckCircle,
	Clock,
	Circle,
} from "lucide-react";
import { api } from "@/lib/trpc/react";
import { CatStatusMenu } from "@/components/cat-status-menu";
import { useSession } from "@/lib/auth/client";
import { useTranslations } from "next-intl";
import { Link } from "@/lib/i18n/navigation";
import { cn } from "@/lib/utils";
import ChatLoading from "./loading";

export default function ChatPage() {
	const params = useParams();
	const router = useRouter();
	const chatId = params.id as string;
	const [newMessage, setNewMessage] = useState("");
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const { data: session } = useSession();
	const t = useTranslations("profile.messages");

	// Helper function to format time like in the reference
	const formatTime = (timestamp: string) => {
		const date = new Date(timestamp);
		const now = new Date();
		const today = new Date(
			now.getFullYear(),
			now.getMonth(),
			now.getDate()
		);
		const messageDate = new Date(
			date.getFullYear(),
			date.getMonth(),
			date.getDate()
		);

		if (messageDate.getTime() === today.getTime()) {
			return date.toLocaleTimeString("en-US", {
				hour: "numeric",
				minute: "2-digit",
				hour12: true,
			});
		} else if (messageDate.getTime() === today.getTime() - 86400000) {
			return "Yesterday";
		} else {
			return date.toLocaleDateString("en-US", {
				month: "short",
				day: "numeric",
			});
		}
	};

	// Helper function to get message status icon
	const getMessageStatus = (status: string) => {
		switch (status) {
			case "sending":
				return <Clock className="w-4 h-4 text-gray-400" />;
			case "delivered":
				return <CheckCircle className="w-4 h-4 text-gray-400" />;
			case "read":
				return <CheckCircle className="w-4 h-4 text-teal-500" />;
			default:
				return <Circle className="w-4 h-4 text-gray-400" />;
		}
	};

	// Get chat and messages
	const { data, isLoading, refetch } = api.messages.getChatMessages.useQuery({
		chatId,
		limit: 50,
	});

	const { data: headerData } = api.messages.getConversationHeader.useQuery({
		chatId,
	});

	const { mutate: sendMessage, isPending: isSending } =
		api.messages.sendMessage.useMutation({
			onSuccess: () => {
				setNewMessage("");
				refetch();
			},
		});

	// Scroll to bottom when messages change
	useEffect(() => {
		if (messagesEndRef.current) {
			messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
		}
	}, [data?.messages]);

	const handleSendMessage = (e: React.FormEvent) => {
		e.preventDefault();
		if (newMessage.trim() && chatId) {
			sendMessage({
				chatId,
				content: newMessage,
			});
		}
	};

	if (isLoading || !data) {
		return <ChatLoading />;
	}

	const otherUser = headerData?.with;
	const catInfo = headerData?.cat;
	const messages = [...data.messages].reverse(); // Reverse to show newest messages at the bottom

	// Check if the current user is the cat owner
	const isCatOwner =
		catInfo &&
		session?.user &&
		"userId" in catInfo &&
		catInfo.userId === session.user.id;

	const isVerified =
		otherUser?.name?.includes("Shelter") ||
		otherUser?.name?.includes("Rescue"); // Mock verification

	return (
		<div className="flex flex-col h-full bg-white">
			{/* Chat Header */}
			<div className="bg-white border-b border-gray-200 p-6">
				<div className="flex items-center justify-between">
					{/* Mobile Back Button */}
					<button
						onClick={() => router.back()}
						className="md:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors mr-3"
					>
						<ArrowLeft className="w-5 h-5 text-gray-600" />
					</button>

					<div className="flex items-center space-x-4 flex-1">
						<div className="relative">
							<Link
								href={`/users/${otherUser?.id}`}
								className="block hover:opacity-80 transition-opacity"
							>
								<Avatar className="w-12 h-12 flex items-center justify-center">
									<AvatarImage
										src={
											otherUser?.image ||
											"/placeholder-user.svg"
										}
										alt={
											otherUser?.name || t("unknownUser")
										}
										className="object-cover w-full h-full rounded-full"
									/>
									<AvatarFallback className="w-full h-full flex items-center justify-center text-sm font-medium bg-gray-100 rounded-full">
										{otherUser?.name
											?.charAt(0)
											?.toUpperCase() || "U"}
									</AvatarFallback>
								</Avatar>
							</Link>
						</div>

						<div>
							<div className="flex items-center space-x-2">
								<Link
									href={`/users/${otherUser?.id}`}
									className="block hover:opacity-80 transition-opacity"
								>
									<h2 className="text-lg font-display font-semibold text-gray-900">
										{otherUser?.name || t("unknownUser")}
									</h2>
								</Link>
								{isVerified && (
									<Shield className="w-5 h-5 text-blue-500" />
								)}
							</div>
							<div className="flex items-center space-x-2">
								{catInfo && (
									<Link
										href={`/cats/${catInfo.id}`}
										className="flex items-center space-x-2 text-sm text-teal-600 hover:text-teal-700"
									>
										<Avatar className="w-5 h-5">
											<AvatarImage
												src={
													catInfo.imageUrl ||
													"/cat.jpeg"
												}
												alt={catInfo.name}
											/>
											<AvatarFallback>
												{catInfo.name
													?.charAt(0)
													?.toUpperCase() || "C"}
											</AvatarFallback>
										</Avatar>
										<span>{catInfo.name}</span>
									</Link>
								)}
							</div>
						</div>
					</div>

					{/* Add status menu if user is cat owner */}
					{isCatOwner && catInfo && (
						<CatStatusMenu
							catId={catInfo.id.toString()}
							currentStatus={
								("status" in catInfo
									? String(catInfo.status)
									: undefined) ||
								("adopted" in catInfo && catInfo.adopted
									? "adopted"
									: "available")
							}
						/>
					)}
				</div>
			</div>

			{/* Messages */}
			<div className="flex-1 overflow-y-auto p-4 space-y-4">
				{messages && messages.length > 0 ? (
					<>
						{messages.map((message, index) => {
							const isCurrentUser = message.isFromMe;
							const showTimestamp =
								index === 0 ||
								new Date(
									messages[index - 1].timestamp
								).getDate() !==
									new Date(message.timestamp).getDate();

							return (
								<div key={message.id} className="space-y-2">
									{/* Date Separator */}
									{showTimestamp && (
										<div className="flex justify-center">
											<span className="px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full">
												{formatTime(message.timestamp)}
											</span>
										</div>
									)}

									{/* Message */}
									<div
										className={cn(
											"flex",
											isCurrentUser
												? "justify-end"
												: "justify-start"
										)}
									>
										<div
											className={cn(
												"max-w-md px-4 py-2 rounded-2xl",
												isCurrentUser
													? "bg-teal-500 text-white ml-12"
													: "bg-white border border-gray-200 text-gray-900 mr-12"
											)}
										>
											<p className="text-sm leading-relaxed">
												{message.content}
											</p>

											{/* Message Status */}
											{isCurrentUser && (
												<div className="flex justify-end mt-1">
													{getMessageStatus(
														"delivered"
													)}
												</div>
											)}
										</div>
									</div>
								</div>
							);
						})}

						<div ref={messagesEndRef} />
					</>
				) : (
					<div className="flex h-full items-center justify-center">
						<div className="text-center">
							<div className="mb-4 rounded-full bg-gray-100 p-3 mx-auto w-fit">
								<Send className="h-6 w-6 text-gray-400" />
							</div>
							<h3 className="font-medium text-gray-900 mb-2">
								{t("startConversation")}
							</h3>
							<p className="text-sm text-gray-600">
								{t("sendToBegin")} {otherUser?.name}.
								{catInfo && (
									<>
										{" "}
										{t("conversationAbout")}{" "}
										<span className="font-medium text-teal-600">
											{catInfo.name}
										</span>
										.
									</>
								)}
							</p>
						</div>
					</div>
				)}
			</div>

			{/* Message Input */}
			<div className="border-t border-gray-200 p-3 md:p-4 bg-white">
				<div className="flex items-end space-x-2 md:space-x-4">
					{/* Message Input */}
					<div className="flex-1 relative">
						<textarea
							value={newMessage}
							onChange={(e) => setNewMessage(e.target.value)}
							onKeyDown={(e) => {
								if (e.key === "Enter" && !e.shiftKey) {
									e.preventDefault();
									handleSendMessage(e);
								}
							}}
							placeholder={
								t("typePlaceholder") || "Type a message..."
							}
							rows={1}
							className="w-full px-3 py-2 md:px-4 md:py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none text-base"
							style={{
								minHeight: "40px",
								maxHeight: "120px",
							}}
							disabled={isSending}
						/>
					</div>

					{/* Send Button */}
					<button
						onClick={handleSendMessage}
						disabled={isSending || !newMessage.trim() || isLoading}
						className={cn(
							"p-2 md:p-3 rounded-xl transition-colors flex-shrink-0",
							newMessage.trim()
								? "bg-teal-500 hover:bg-teal-600 text-white"
								: "bg-gray-100 text-gray-400 cursor-not-allowed"
						)}
					>
						<Send className="w-4 h-4 md:w-5 md:h-5" />
					</button>
				</div>
			</div>
		</div>
	);
}
