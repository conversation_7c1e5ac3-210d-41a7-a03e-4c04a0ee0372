"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Search, Plus } from "lucide-react";
import { SafeImage } from "@/components/safe-image";
import { Skeleton } from "@/components/ui/skeleton";
import { api } from "@/lib/trpc/react";
import { formatRelativeTime } from "@/lib/utils/date-formatter";
import { useTranslations } from "next-intl";
import { Link } from "@/lib/i18n/navigation";

export function Messages() {
	const [searchQuery, setSearchQuery] = useState("");
	const t = useTranslations("profile.messages");
	const catsT = useTranslations("cats");
	const rolesT = useTranslations("profile.roles");

	// Fetch conversations using optimized tRPC query
	const { data: conversationsData, isLoading } =
		api.messages.getMyConversations.useQuery({ page: 1, limit: 50 });

	const conversations = conversationsData?.conversations || [];

	const filteredConversations =
		conversations?.filter(
			(conv) =>
				conv.with?.name
					.toLowerCase()
					.includes(searchQuery.toLowerCase()) ||
				(conv.cat?.name &&
					conv.cat.name
						.toLowerCase()
						.includes(searchQuery.toLowerCase())) ||
				conv.lastMessage?.text
					.toLowerCase()
					.includes(searchQuery.toLowerCase())
		) || [];

	if (isLoading) {
		return <MessagesLoadingSkeleton />;
	}

	if (!conversations || conversations.length === 0) {
		return (
			<div className="text-center py-12">
				<h3 className="text-xl font-medium mb-2">
					{t("noMessagesYet")}
				</h3>
				<p className="text-muted-foreground mb-6">
					{t("noMessagesDescription")}
				</p>
				<Button asChild>
					<Link href="/cats">
						<Plus className="h-4 w-4 mr-2" />
						{catsT("title")}
					</Link>
				</Button>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<div className="relative flex-1 max-w-sm">
					<Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
					<Input
						type="search"
						placeholder={t("searchPlaceholder")}
						className="pl-8"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
					/>
				</div>
			</div>

			<div className="space-y-2">
				{filteredConversations.length === 0 ? (
					<p className="text-center text-muted-foreground py-8">
						{t("noMatchingConversations")}
					</p>
				) : (
					filteredConversations.map((conversation) => (
						<Link
							href={`/profile/messages/${conversation.id}`}
							key={conversation.id}
						>
							<Card
								className={`hover:bg-muted/50 transition-colors ${conversation.lastMessage && !conversation.lastMessage.isRead && !conversation.lastMessage.isFromMe ? "border-primary" : ""}`}
							>
								<CardContent className="p-4">
									<div className="flex items-start gap-4">
										<div className="relative h-12 w-12 rounded-full overflow-hidden bg-muted shrink-0">
											<SafeImage
												src={
													conversation.with?.image ||
													"/placeholder-user.svg?height=100&width=100"
												}
												alt={
													conversation.with?.name ||
													t("unknownUser")
												}
												className="object-cover"
											/>
										</div>
										<div className="flex-1 min-w-0">
											<div className="flex justify-between items-start">
												<div className="flex items-center gap-2">
													<h3 className="font-medium truncate">
														{conversation.with
															?.name ||
															t("unknownUser")}
													</h3>
													{conversation.with
														?.role && (
														<RoleBadge
															role={
																conversation
																	.with.role
															}
														/>
													)}
												</div>
												<span className="text-xs text-muted-foreground whitespace-nowrap">
													{conversation.lastMessage
														? formatRelativeTime(
																conversation
																	.lastMessage
																	.timestamp
															)
														: ""}
												</span>
											</div>

											{conversation.cat && (
												<p className="text-xs text-muted-foreground mb-1">
													{t("regarding")}{" "}
													<span className="text-primary">
														{conversation.cat.name}
													</span>
												</p>
											)}

											{conversation.lastMessage && (
												<div className="flex items-start gap-1">
													{conversation.lastMessage
														.isFromMe && (
														<span className="text-xs text-muted-foreground mt-0.5">
															{t("you")}:
														</span>
													)}
													<p
														className={`text-sm truncate ${!conversation.lastMessage.isRead && !conversation.lastMessage.isFromMe ? "font-medium" : "text-muted-foreground"}`}
													>
														{
															conversation
																.lastMessage
																.text
														}
													</p>
												</div>
											)}

											{conversation.lastMessage &&
												!conversation.lastMessage
													.isRead &&
												!conversation.lastMessage
													.isFromMe && (
													<div className="mt-1">
														<Badge
															variant="default"
															className="text-xs"
														>
															{t("new")}
														</Badge>
													</div>
												)}
										</div>
									</div>
								</CardContent>
							</Card>
						</Link>
					))
				)}
			</div>
		</div>
	);
}

function MessagesLoadingSkeleton() {
	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<Skeleton className="h-10 w-64" />
			</div>

			<div className="space-y-2">
				{[1, 2, 3].map((i) => (
					<Card key={i}>
						<CardContent className="p-4">
							<div className="flex items-start gap-4">
								<Skeleton className="h-12 w-12 rounded-full" />
								<div className="flex-1">
									<div className="flex justify-between items-start">
										<Skeleton className="h-5 w-32 mb-2" />
										<Skeleton className="h-4 w-16" />
									</div>
									<Skeleton className="h-4 w-full mb-2" />
									<Skeleton className="h-4 w-3/4" />
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}

function RoleBadge({ role }: { role: string }) {
	const rolesT = useTranslations("profile.roles");

	switch (role) {
		case "admin":
			return (
				<Badge className="bg-purple-500 text-xs">
					{rolesT("admin")}
				</Badge>
			);
		case "rescuer":
			return (
				<Badge className="bg-blue-500 text-xs">
					{rolesT("rescuer")}
				</Badge>
			);
		case "clinic":
			return (
				<Badge className="bg-green-500 text-xs">
					{rolesT("clinic")}
				</Badge>
			);
		case "adopter":
			return (
				<Badge variant="outline" className="text-xs">
					{rolesT("adopter")}
				</Badge>
			);
		default:
			return null;
	}
}
